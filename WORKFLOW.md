# Detailed Workflow Documentation

## 🔄 Complete System Workflow

### Phase 1: Hardware Data Acquisition

#### 1.1 Load Cell Operation
```
Physical Weight → Mechanical Deformation → Electrical Signal
```

**Bending Beam Load Cell Mechanism:**
1. **Applied Force**: Medical package placed on load cell platform
2. **Beam Deformation**: Internal metal beam bends proportionally to weight
3. **Strain Gauge Response**: Resistance change in bonded strain gauges
4. **Wheatstone Bridge**: Converts resistance change to voltage signal
5. **Signal Characteristics**: Millivolt-level analog output

#### 1.2 Signal Conditioning Chain
```
Load Cell → HX711 ADC → Microcontroller → Digital Processing
```

**HX711 ADC Processing:**
- **Resolution**: 24-bit precision
- **Gain**: Programmable amplification (32x, 64x, 128x)
- **Sampling Rate**: Up to 80 Hz
- **Noise Rejection**: Built-in filtering
- **Output**: Serial digital data stream

#### 1.3 Microcontroller Interface
**Raspberry Pi Pico Implementation:**
- **GPIO Pins**: Clock and data lines to HX711
- **Sampling**: Continuous reading at 50-100 Hz
- **Buffer Management**: Circular buffer for real-time processing
- **Data Logging**: CSV format with timestamps

### Phase 2: Enhanced Data Quality and Preprocessing

#### 2.1 Data Quality Assurance
```python
# Enhanced data quality pipeline
def ensure_data_quality(X_train, y_train):
    # Step 1: Remove duplicate rows (354 found in dataset)
    train_data = pd.concat([X_train, y_train], axis=1)
    train_data_unique = train_data.drop_duplicates()
    X_train_clean = train_data_unique.drop('Label', axis=1)
    y_train_clean = train_data_unique['Label']

    # Step 2: Check class imbalance (4.11 ratio detected)
    imbalance_ratio = y_train_clean.value_counts().max() / y_train_clean.value_counts().min()
    if imbalance_ratio > 3:
        use_stratified_sampling = True

    return X_train_clean, y_train_clean, use_stratified_sampling
```

#### 2.2 Raw Data Preprocessing
```python
# Pseudo-code for real-time processing
def preprocess_signal(raw_readings):
    # Remove DC offset
    signal = raw_readings - np.mean(raw_readings)

    # Apply moving average filter
    filtered_signal = moving_average(signal, window=10)

    # Kalman filtering for noise reduction
    kalman_filtered = kalman_filter(filtered_signal)

    return kalman_filtered
```

#### 2.2 Feature Extraction Engine

**Time-Domain Features (Real-time calculation):**
```python
def extract_time_features(signal_window):
    features = {
        'mean': np.mean(signal_window),
        'std': np.std(signal_window),
        'rms': np.sqrt(np.mean(signal_window**2)),
        'peak_to_peak': np.max(signal_window) - np.min(signal_window),
        'skewness': scipy.stats.skew(signal_window),
        'kurtosis': scipy.stats.kurtosis(signal_window)
    }
    return features
```

**Frequency-Domain Features:**
```python
def extract_frequency_features(signal_window):
    fft = np.fft.fft(signal_window)
    fft_magnitude = np.abs(fft[:len(fft)//2])
    
    features = {
        'dominant_freq_magnitude': np.max(fft_magnitude[1:]),
        'spectral_energy': np.sum(fft_magnitude**2),
        'spectral_centroid': np.sum(np.arange(len(fft_magnitude)) * fft_magnitude) / np.sum(fft_magnitude)
    }
    return features
```

#### 2.4 Robust Feature Selection
```python
# Enhanced feature selection with error handling
def robust_feature_selection(X_train, y_train):
    # Method 1: Correlation-based (most robust for noisy data)
    correlations = X_train.corrwith(y_train).abs().sort_values(ascending=False)
    correlations = correlations.dropna()  # Handle NaN correlations
    top_features = correlations.head(100).index.tolist()

    # Method 2: Fallback methods with try-catch
    try:
        # F-test, RFE, Lasso selection with error handling
        selected_features = apply_advanced_selection(X_train[top_features], y_train)
    except Exception as e:
        print(f"Advanced selection failed: {e}. Using correlation-based selection.")
        selected_features = top_features

    return selected_features
```

#### 2.5 Moving Window Processing
```
Continuous Stream: [x1, x2, x3, ..., xn]
Window 1: [x1:x100] → Quality Check → Features → Prediction
Window 2: [x2:x101] → Quality Check → Features → Prediction
Window 3: [x3:x102] → Quality Check → Features → Prediction
```

### Phase 3: Machine Learning Inference Pipeline

#### 3.1 Feature Vector Assembly
```python
def assemble_feature_vector(time_features, freq_features, selected_raw):
    # Combine all feature types
    feature_vector = np.concatenate([
        list(time_features.values()),
        list(freq_features.values()),
        selected_raw  # Top correlated raw features
    ])
    return feature_vector
```

#### 3.2 Stacking Ensemble Prediction

**Level-0 Base Models Inference:**
```python
def level0_predictions(feature_vector):
    predictions = {}
    
    # Decision Tree
    predictions['dt'] = decision_tree_model.predict([feature_vector])[0]
    
    # Random Forest
    predictions['rf'] = random_forest_model.predict([feature_vector])[0]
    
    # Gradient Boosting
    predictions['gb'] = gradient_boosting_model.predict([feature_vector])[0]
    
    # XGBoost
    predictions['xgb'] = xgboost_model.predict([feature_vector])[0]
    
    # LightGBM
    predictions['lgb'] = lightgbm_model.predict([feature_vector])[0]
    
    return list(predictions.values())
```

**Level-1 Meta-Learner:**
```python
def level1_prediction(base_predictions):
    # Ridge regression combines base predictions
    final_prediction = ridge_meta_model.predict([base_predictions])[0]
    return final_prediction
```

#### 3.3 Real-Time Decision Making
```python
def make_decision(predicted_weight, target_weight, tolerance=2.0):
    error = abs(predicted_weight - target_weight)
    
    if error <= tolerance:
        return "ACCEPT", error
    else:
        return "REJECT", error
```

### Phase 4: Quality Control Integration

#### 4.1 Conveyor Belt Integration
```
Package → Load Cell → Weight Prediction → Decision → Action
```

**Control Flow:**
1. **Package Detection**: Sensor triggers measurement
2. **Stabilization**: Wait for stable reading (100ms)
3. **Measurement**: Collect sensor data window
4. **Processing**: DSP + ML pipeline
5. **Decision**: Accept/Reject based on prediction
6. **Action**: Pneumatic pusher or conveyor routing

#### 4.2 Production Line Workflow
```
[Package Input] → [Weight Station] → [ML Processing] → [Decision Gate] → [Accept/Reject]
       ↓               ↓                    ↓              ↓              ↓
   RFID Scan    Load Cell Reading    Feature Extract.  Threshold Check  Route Package
```

### Phase 5: System Monitoring & Maintenance

#### 5.1 Performance Monitoring
```python
def monitor_system_performance():
    metrics = {
        'prediction_accuracy': calculate_accuracy(),
        'processing_latency': measure_latency(),
        'false_positive_rate': calculate_fpr(),
        'false_negative_rate': calculate_fnr(),
        'system_uptime': get_uptime()
    }
    return metrics
```

#### 5.2 Model Retraining Pipeline
```
New Data Collection → Feature Engineering → Model Retraining → Validation → Deployment
```

**Retraining Triggers:**
- Performance degradation below threshold
- Significant drift in data distribution
- New product types introduction
- Scheduled periodic updates

### Phase 6: Deployment Architecture

#### 6.1 Embedded System Implementation
```
┌─────────────────┐    ┌──────────────┐    ┌─────────────────┐
│   Load Cell     │───▶│    HX711     │───▶│ Microcontroller │
│  (Analog Out)   │    │   (24-bit    │    │  (Digital Proc) │
│                 │    │    ADC)      │    │                 │
└─────────────────┘    └──────────────┘    └─────────────────┘
                                                     │
                                                     ▼
┌─────────────────┐    ┌──────────────┐    ┌─────────────────┐
│   Decision      │◀───│  ML Pipeline │◀───│  DSP Pipeline   │
│   Output        │    │  (Stacking   │    │  (Feature       │
│ (Accept/Reject) │    │   Ensemble)  │    │  Extraction)    │
└─────────────────┘    └──────────────┘    └─────────────────┘
```

#### 6.2 Real-Time Constraints
- **Latency**: <100ms total processing time
- **Memory**: <2MB for model storage
- **CPU**: ARM Cortex-M4 @ 168MHz sufficient
- **Power**: <500mW continuous operation

### Phase 7: Calibration & Validation

#### 7.1 System Calibration
```python
def calibrate_system():
    # Known weight calibration
    known_weights = [50, 60, 70, 80, 90, 100]  # grams
    
    for weight in known_weights:
        readings = collect_readings(weight, samples=100)
        calibration_data[weight] = np.mean(readings)
    
    # Linear calibration curve
    calibration_curve = fit_calibration_curve(calibration_data)
    return calibration_curve
```

#### 7.2 Validation Protocol
1. **Accuracy Testing**: Known weights across full range
2. **Repeatability**: Multiple measurements of same weight
3. **Linearity**: Response across weight range
4. **Temperature Stability**: Performance across temperature range
5. **Long-term Drift**: Stability over extended operation

### Phase 8: Error Handling & Recovery

#### 8.1 Fault Detection
```python
def detect_faults(sensor_reading):
    faults = []
    
    # Out of range detection
    if sensor_reading < MIN_READING or sensor_reading > MAX_READING:
        faults.append("SENSOR_OUT_OF_RANGE")
    
    # Noise level detection
    if calculate_noise_level(sensor_reading) > NOISE_THRESHOLD:
        faults.append("HIGH_NOISE_LEVEL")
    
    # Drift detection
    if abs(sensor_reading - baseline) > DRIFT_THRESHOLD:
        faults.append("SENSOR_DRIFT")
    
    return faults
```

#### 8.2 Recovery Procedures
- **Sensor Recalibration**: Automatic zero adjustment
- **Model Fallback**: Switch to simpler backup model
- **Manual Override**: Operator intervention capability
- **System Reset**: Complete system restart if needed

## 🔧 Implementation Guidelines

### Hardware Setup
1. Mount load cell on stable platform
2. Connect HX711 with short, shielded cables
3. Ensure proper grounding and power supply
4. Calibrate with known weights

### Software Deployment
1. Flash microcontroller with trained model
2. Configure sampling parameters
3. Set decision thresholds
4. Enable monitoring and logging

### Testing & Validation
1. Verify accuracy with test weights
2. Check processing latency
3. Validate decision thresholds
4. Test fault handling procedures

This workflow ensures robust, accurate, and reliable weight prediction for medical packaging applications while maintaining real-time performance requirements.
