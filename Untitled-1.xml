<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 1622.237548828125 2960" style="max-width: 1622.237548828125px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-64433992-1681-4814-b9ac-2cc178920fa8"><style>#mermaid-64433992-1681-4814-b9ac-2cc178920fa8{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .error-icon{fill:#a44141;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .edge-thickness-normal{stroke-width:1px;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .marker.cross{stroke:lightgrey;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 p{margin:0;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .cluster-label text{fill:#F9FFFE;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .cluster-label span{color:#F9FFFE;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .cluster-label span p{background-color:transparent;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .label text,#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 span{fill:#ccc;color:#ccc;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .node rect,#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .node circle,#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .node ellipse,#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .node polygon,#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .rough-node .label text,#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .node .label text,#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .image-shape .label,#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .icon-shape .label{text-anchor:middle;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .rough-node .label,#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .node .label,#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .image-shape .label,#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .icon-shape .label{text-align:center;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .node.clickable{cursor:pointer;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .arrowheadPath{fill:lightgrey;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .cluster text{fill:#F9FFFE;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .cluster span{color:#F9FFFE;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 rect.text{fill:none;stroke-width:0;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .icon-shape,#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .icon-shape p,#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .icon-shape rect,#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .hardware&gt;*{fill:#e1f5fe!important;stroke:#01579b!important;stroke-width:2px!important;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .hardware span{fill:#e1f5fe!important;stroke:#01579b!important;stroke-width:2px!important;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .dsp&gt;*{fill:#f3e5f5!important;stroke:#4a148c!important;stroke-width:2px!important;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .dsp span{fill:#f3e5f5!important;stroke:#4a148c!important;stroke-width:2px!important;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .ml&gt;*{fill:#e8f5e8!important;stroke:#1b5e20!important;stroke-width:2px!important;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .ml span{fill:#e8f5e8!important;stroke:#1b5e20!important;stroke-width:2px!important;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .decision&gt;*{fill:#fff3e0!important;stroke:#e65100!important;stroke-width:2px!important;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .decision span{fill:#fff3e0!important;stroke:#e65100!important;stroke-width:2px!important;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .control&gt;*{fill:#fce4ec!important;stroke:#880e4f!important;stroke-width:2px!important;}#mermaid-64433992-1681-4814-b9ac-2cc178920fa8 .control span{fill:#fce4ec!important;stroke:#880e4f!important;stroke-width:2px!important;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-64433992-1681-4814-b9ac-2cc178920fa8_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-64433992-1681-4814-b9ac-2cc178920fa8_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-64433992-1681-4814-b9ac-2cc178920fa8_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-64433992-1681-4814-b9ac-2cc178920fa8_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-64433992-1681-4814-b9ac-2cc178920fa8_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-64433992-1681-4814-b9ac-2cc178920fa8_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"><g data-look="classic" id="Control" class="cluster"><rect height="352" width="545.625" y="2600" x="787.2218818664551" style=""></rect><g transform="translate(992.6343803405762, 2600)" class="cluster-label"><foreignObject height="24" width="134.8000030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🎮 Control System</p></span></div></foreignObject></g></g><g data-look="classic" id="Decision" class="cluster"><rect height="352" width="538.3937454223633" y="2198" x="790.1718864440918" style=""></rect><g transform="translate(989.2812576293945, 2198)" class="cluster-label"><foreignObject height="24" width="140.1750030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>⚖️ Decision Making</p></span></div></foreignObject></g></g><g data-look="classic" id="ML" class="cluster"><rect height="804" width="1410.925006866455" y="1344" x="203.31250381469727" style=""></rect><g transform="translate(808.7750072479248, 1344)" class="cluster-label"><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🤖 Machine Learning Pipeline</p></span></div></foreignObject></g></g><g data-look="classic" id="DSP" class="cluster"><rect height="580" width="993.1406364440918" y="714" x="8" style=""></rect><g transform="translate(405.1015682220459, 714)" class="cluster-label"><foreignObject height="24" width="198.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔊 Digital Signal Processing</p></span></div></foreignObject></g></g><g data-look="classic" id="Hardware" class="cluster"><rect height="656" width="329.23748779296875" y="8" x="8.462509155273438" style=""></rect><g transform="translate(102.96875, 8)" class="cluster-label"><foreignObject height="24" width="140.22500610351562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔧 Hardware Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="Level1" class="cluster"><rect height="176" width="1146.337501525879" y="1771" x="391.56250762939453" style=""></rect><g transform="translate(874.8125038146973, 1771)" class="cluster-label"><foreignObject height="24" width="179.83750915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🎯 Level-1 Meta-Learner</p></span></div></foreignObject></g></g><g data-look="classic" id="Level0" class="cluster"><rect height="176" width="1284.1750030517578" y="1545" x="310.06250762939453" style=""></rect><g transform="translate(867.0250091552734, 1545)" class="cluster-label"><foreignObject height="24" width="170.25"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🎯 Level-0 Base Models</p></span></div></foreignObject></g></g><g data-look="classic" id="FeatureExt" class="cluster"><rect height="176" width="895.7593879699707" y="1093" x="85.359375" style=""></rect><g transform="translate(452.79532051086426, 1093)" class="cluster-label"><foreignObject height="24" width="160.8874969482422"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>⚙️ Feature Extraction</p></span></div></foreignObject></g></g><g data-look="classic" id="Filtering" class="cluster"><rect height="152" width="818.9000091552734" y="891" x="28" style=""></rect><g transform="translate(372.2062530517578, 891)" class="cluster-label"><foreignObject height="24" width="130.4875030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔍 Noise Filtering</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#mermaid-64433992-1681-4814-b9ac-2cc178920fa8_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Package_LoadCell_0" d="M173.081,111L173.081,115.167C173.081,119.333,173.081,127.667,173.081,135.333C173.081,143,173.081,150,173.081,153.5L173.081,157"></path><path marker-end="url(#mermaid-64433992-1681-4814-b9ac-2cc178920fa8_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LoadCell_HX711_1" d="M173.081,287L173.081,291.167C173.081,295.333,173.081,303.667,173.081,311.333C173.081,319,173.081,326,173.081,329.5L173.081,333"></path><path marker-end="url(#mermaid-64433992-1681-4814-b9ac-2cc178920fa8_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_HX711_MCU_2" d="M173.081,463L173.081,467.167C173.081,471.333,173.081,479.667,173.081,487.333C173.081,495,173.081,502,173.081,505.5L173.081,509"></path><path marker-end="url(#mermaid-64433992-1681-4814-b9ac-2cc178920fa8_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MCU_RawData_3" d="M173.081,639L173.081,643.167C173.081,647.333,173.081,655.667,173.081,664C173.081,672.333,173.081,680.667,173.081,689C173.081,697.333,173.081,705.667,173.081,713.333C173.081,721,173.081,728,173.081,731.5L173.081,735"></path><path marker-end="url(#mermaid-64433992-1681-4814-b9ac-2cc178920fa8_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TimeDomain_FeatureVector_8" d="M307.344,1244L307.344,1248.167C307.344,1252.333,307.344,1260.667,307.344,1269C307.344,1277.333,307.344,1285.667,307.344,1294C307.344,1302.333,307.344,1310.667,307.344,1319C307.344,1327.333,307.344,1335.667,313.035,1343.63C318.726,1351.593,330.109,1359.187,335.8,1362.983L341.491,1366.78"></path><path marker-end="url(#mermaid-64433992-1681-4814-b9ac-2cc178920fa8_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_FreqDomain_FeatureVector_9" d="M571.169,1244L571.169,1248.167C571.169,1252.333,571.169,1260.667,571.169,1269C571.169,1277.333,571.169,1285.667,571.169,1294C571.169,1302.333,571.169,1310.667,571.169,1319C571.169,1327.333,571.169,1335.667,565.477,1343.63C559.786,1351.593,548.404,1359.187,542.712,1362.983L537.021,1366.78"></path><path marker-end="url(#mermaid-64433992-1681-4814-b9ac-2cc178920fa8_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Statistical_FeatureVector_10" d="M838.844,1244L838.844,1248.167C838.844,1252.333,838.844,1260.667,838.844,1269C838.844,1277.333,838.844,1285.667,838.844,1294C838.844,1302.333,838.844,1310.667,838.844,1319C838.844,1327.333,838.844,1335.667,789.008,1350.808C739.173,1365.95,639.502,1387.9,589.667,1398.876L539.831,1409.851"></path><path marker-end="url(#mermaid-64433992-1681-4814-b9ac-2cc178920fa8_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DT_Ridge_12" d="M439.256,1696L439.256,1700.167C439.256,1704.333,439.256,1712.667,439.256,1721C439.256,1729.333,439.256,1737.667,439.256,1746C439.256,1754.333,439.256,1762.667,544.925,1779.214C650.593,1795.762,861.931,1820.525,967.599,1832.906L1073.268,1845.287"></path><path marker-end="url(#mermaid-64433992-1681-4814-b9ac-2cc178920fa8_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_RF_Ridge_13" d="M680.256,1696L680.256,1700.167C680.256,1704.333,680.256,1712.667,680.256,1721C680.256,1729.333,680.256,1737.667,680.256,1746C680.256,1754.333,680.256,1762.667,745.763,1778.135C811.27,1793.604,942.285,1816.209,1007.792,1827.511L1073.299,1838.813"></path><path marker-end="url(#mermaid-64433992-1681-4814-b9ac-2cc178920fa8_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_GB_Ridge_14" d="M934.431,1696L934.431,1700.167C934.431,1704.333,934.431,1712.667,934.431,1721C934.431,1729.333,934.431,1737.667,934.431,1746C934.431,1754.333,934.431,1762.667,957.602,1774.802C980.774,1786.938,1027.116,1802.876,1050.287,1810.845L1073.458,1818.814"></path><path marker-end="url(#mermaid-64433992-1681-4814-b9ac-2cc178920fa8_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_XGB_Ridge_15" d="M1202.844,1696L1202.844,1700.167C1202.844,1704.333,1202.844,1712.667,1202.844,1721C1202.844,1729.333,1202.844,1737.667,1202.844,1746C1202.844,1754.333,1202.844,1762.667,1202.344,1770.34C1201.844,1778.013,1200.845,1785.027,1200.345,1788.533L1199.845,1792.04"></path><path marker-end="url(#mermaid-64433992-1681-4814-b9ac-2cc178920fa8_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LGB_Ridge_16" d="M1461.563,1696L1461.563,1700.167C1461.563,1704.333,1461.563,1712.667,1461.563,1721C1461.563,1729.333,1461.563,1737.667,1461.563,1746C1461.563,1754.333,1461.563,1762.667,1435.83,1775.181C1410.098,1787.696,1358.634,1804.391,1332.902,1812.739L1307.17,1821.087"></path><path marker-end="url(#mermaid-64433992-1681-4814-b9ac-2cc178920fa8_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Ridge_FinalPred_17" d="M1190.303,1922L1190.303,1926.167C1190.303,1930.333,1190.303,1938.667,1190.303,1947C1190.303,1955.333,1190.303,1963.667,1190.303,1971.333C1190.303,1979,1190.303,1986,1190.303,1989.5L1190.303,1993"></path><path marker-end="url(#mermaid-64433992-1681-4814-b9ac-2cc178920fa8_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_FinalPred_Threshold_18" d="M1190.303,2123L1190.303,2127.167C1190.303,2131.333,1190.303,2139.667,1190.303,2148C1190.303,2156.333,1190.303,2164.667,1190.303,2173C1190.303,2181.333,1190.303,2189.667,1190.303,2197.333C1190.303,2205,1190.303,2212,1190.303,2215.5L1190.303,2219"></path><path marker-end="url(#mermaid-64433992-1681-4814-b9ac-2cc178920fa8_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Threshold_Accept_19" d="M1087.041,2320.576L1060.449,2329.48C1033.857,2338.384,980.674,2356.192,954.082,2368.596C927.491,2381,927.491,2388,927.491,2391.5L927.491,2395"></path><path marker-end="url(#mermaid-64433992-1681-4814-b9ac-2cc178920fa8_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Threshold_Reject_20" d="M1190.303,2349L1190.303,2353.167C1190.303,2357.333,1190.303,2365.667,1190.303,2373.333C1190.303,2381,1190.303,2388,1190.303,2391.5L1190.303,2395"></path><path marker-end="url(#mermaid-64433992-1681-4814-b9ac-2cc178920fa8_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Accept_Conveyor_21" d="M927.491,2525L927.491,2529.167C927.491,2533.333,927.491,2541.667,927.491,2550C927.491,2558.333,927.491,2566.667,927.491,2575C927.491,2583.333,927.491,2591.667,927.491,2599.333C927.491,2607,927.491,2614,927.491,2617.5L927.491,2621"></path><path marker-end="url(#mermaid-64433992-1681-4814-b9ac-2cc178920fa8_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Reject_Pneumatic_22" d="M1190.303,2525L1190.303,2529.167C1190.303,2533.333,1190.303,2541.667,1190.303,2550C1190.303,2558.333,1190.303,2566.667,1190.303,2575C1190.303,2583.333,1190.303,2591.667,1190.303,2599.333C1190.303,2607,1190.303,2614,1190.303,2617.5L1190.303,2621"></path><path marker-end="url(#mermaid-64433992-1681-4814-b9ac-2cc178920fa8_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Conveyor_Sorting_23" d="M927.491,2751L927.491,2755.167C927.491,2759.333,927.491,2767.667,954.033,2780.721C980.576,2793.775,1033.662,2811.55,1060.205,2820.438L1086.748,2829.326"></path><path marker-end="url(#mermaid-64433992-1681-4814-b9ac-2cc178920fa8_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Pneumatic_Sorting_24" d="M1190.303,2751L1190.303,2755.167C1190.303,2759.333,1190.303,2767.667,1190.303,2775.333C1190.303,2783,1190.303,2790,1190.303,2793.5L1190.303,2797"></path><path marker-end="url(#mermaid-64433992-1681-4814-b9ac-2cc178920fa8_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_RawData_Filtering_4" d="M173.081,841L173.081,845.167C173.081,849.333,173.081,857.667,173.081,865.333C173.081,873,173.081,880,173.081,883.5L173.081,887"></path><path marker-end="url(#mermaid-64433992-1681-4814-b9ac-2cc178920fa8_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MovingAvg_FeatureExt_5" d="M173.081,1018L173.081,1022.167C173.081,1026.333,173.081,1034.667,173.081,1043C173.081,1051.333,173.081,1059.667,173.081,1067.333C173.081,1075,173.081,1082,173.081,1085.5L173.081,1089"></path><path marker-end="url(#mermaid-64433992-1681-4814-b9ac-2cc178920fa8_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Kalman_FeatureExt_6" d="M438.625,1018L438.625,1022.167C438.625,1026.333,438.625,1034.667,438.625,1043C438.625,1051.333,438.625,1059.667,438.625,1067.333C438.625,1075,438.625,1082,438.625,1085.5L438.625,1089"></path><path marker-end="url(#mermaid-64433992-1681-4814-b9ac-2cc178920fa8_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Butterworth_FeatureExt_7" d="M702.994,1018L702.994,1022.167C702.994,1026.333,702.994,1034.667,702.994,1043C702.994,1051.333,702.994,1059.667,702.994,1067.333C702.994,1075,702.994,1082,702.994,1085.5L702.994,1089"></path><path marker-end="url(#mermaid-64433992-1681-4814-b9ac-2cc178920fa8_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_FeatureVector_Level0_11" d="M439.256,1495L439.256,1499.167C439.256,1503.333,439.256,1511.667,439.256,1519.333C439.256,1527,439.256,1534,439.256,1537.5L439.256,1541"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(173.0812530517578, 72)" id="flowchart-Package-0" class="node default hardware"><rect height="78" width="203.9499969482422" y="-39" x="-101.9749984741211" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-71.9749984741211, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="143.9499969482422"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📦 Medical Package<br>50-100g</p></span></div></foreignObject></g></g><g transform="translate(173.0812530517578, 224)" id="flowchart-LoadCell-1" class="node default hardware"><rect height="126" width="259.2375030517578" y="-63" x="-129.6187515258789" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-99.6187515258789, -48)" style="" class="label"><rect></rect><foreignObject height="96" width="199.2375030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>⚖️ Bending Beam Load Cell<br>• Strain Gauges<br>• Wheatstone Bridge<br>• mV Output</p></span></div></foreignObject></g></g><g transform="translate(173.0812530517578, 400)" id="flowchart-HX711-2" class="node default hardware"><rect height="126" width="213.52500915527344" y="-63" x="-106.76250457763672" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-76.76250457763672, -48)" style="" class="label"><rect></rect><foreignObject height="96" width="153.52500915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔌 HX711 ADC<br>• 24-bit Resolution<br>• Programmable Gain<br>• 80Hz Max Rate</p></span></div></foreignObject></g></g><g transform="translate(173.0812530517578, 576)" id="flowchart-MCU-3" class="node default hardware"><rect height="126" width="196.21250915527344" y="-63" x="-98.10625457763672" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-68.10625457763672, -48)" style="" class="label"><rect></rect><foreignObject height="96" width="136.21250915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🖥️ Microcontroller<br>• Raspberry Pi Pico<br>• STM32<br>• ARM Cortex-M</p></span></div></foreignObject></g></g><g transform="translate(173.0812530517578, 790)" id="flowchart-RawData-4" class="node default dsp"><rect height="102" width="213.10000610351562" y="-51" x="-106.55000305175781" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-76.55000305175781, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="153.10000610351562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📊 Raw Sensor Data<br>• 50-100 Hz Sampling<br>• Time Series</p></span></div></foreignObject></g></g><g transform="translate(173.0812530517578, 967)" id="flowchart-MovingAvg-5" class="node default dsp"><rect height="102" width="220.16250610351562" y="-51" x="-110.08125305175781" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-80.08125305175781, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="160.16250610351562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📈 Moving Average<br>• Window: 10, 50, 100<br>• Smoothing</p></span></div></foreignObject></g></g><g transform="translate(438.62500762939453, 967)" id="flowchart-Kalman-6" class="node default dsp"><rect height="102" width="210.9250030517578" y="-51" x="-105.4625015258789" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-75.4625015258789, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="150.9250030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🎯 Kalman Filter<br>• Optimal Estimation<br>• Noise Reduction</p></span></div></foreignObject></g></g><g transform="translate(702.9937591552734, 967)" id="flowchart-Butterworth-7" class="node default dsp"><rect height="102" width="217.8125" y="-51" x="-108.90625" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-78.90625, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="157.8125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🌊 Butterworth Filter<br>• Flat Response<br>• Frequency Filtering</p></span></div></foreignObject></g></g><g transform="translate(307.34375381469727, 1181)" id="flowchart-TimeDomain-8" class="node default dsp"><rect height="126" width="206.85000610351562" y="-63" x="-103.42500305175781" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-73.42500305175781, -48)" style="" class="label"><rect></rect><foreignObject height="96" width="146.85000610351562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>⏱️ Time Domain<br>• Mean, Std, RMS<br>• Skewness, Kurtosis<br>• Peak-to-Peak</p></span></div></foreignObject></g></g><g transform="translate(571.168758392334, 1181)" id="flowchart-FreqDomain-9" class="node default dsp"><rect height="126" width="220.8000030517578" y="-63" x="-110.4000015258789" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-80.4000015258789, -48)" style="" class="label"><rect></rect><foreignObject height="96" width="160.8000030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📡 Frequency Domain<br>• FFT Analysis<br>• Dominant Frequency<br>• Spectral Energy</p></span></div></foreignObject></g></g><g transform="translate(838.8437614440918, 1181)" id="flowchart-Statistical-10" class="node default dsp"><rect height="126" width="214.5500030517578" y="-63" x="-107.2750015258789" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-77.2750015258789, -48)" style="" class="label"><rect></rect><foreignObject height="96" width="154.5500030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📊 Statistical<br>• Correlation Analysis<br>• F-test Selection<br>• PCA Reduction</p></span></div></foreignObject></g></g><g transform="translate(439.2562561035156, 1432)" id="flowchart-FeatureVector-11" class="node default dsp"><rect height="126" width="193.33750915527344" y="-63" x="-96.66875457763672" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-66.66875457763672, -48)" style="" class="label"><rect></rect><foreignObject height="96" width="133.33750915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📋 Feature Vector<br>• ~200 Features<br>• Normalized<br>• Scaled</p></span></div></foreignObject></g></g><g transform="translate(439.2562561035156, 1633)" id="flowchart-DT-12" class="node default ml"><rect height="126" width="188.3874969482422" y="-63" x="-94.1937484741211" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-64.1937484741211, -48)" style="" class="label"><rect></rect><foreignObject height="96" width="128.3874969482422"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🌳 Decision Tree<br>• Max Depth: 8<br>• Min Samples: 50<br>• Regularized</p></span></div></foreignObject></g></g><g transform="translate(680.2562561035156, 1633)" id="flowchart-RF-13" class="node default ml"><rect height="126" width="193.6125030517578" y="-63" x="-96.8062515258789" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-66.8062515258789, -48)" style="" class="label"><rect></rect><foreignObject height="96" width="133.6125030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🌲 Random Forest<br>• 200 Estimators<br>• Bootstrap: True<br>• OOB Score</p></span></div></foreignObject></g></g><g transform="translate(934.4312591552734, 1633)" id="flowchart-GB-14" class="node default ml"><rect height="126" width="214.7375030517578" y="-63" x="-107.3687515258789" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-77.3687515258789, -48)" style="" class="label"><rect></rect><foreignObject height="96" width="154.7375030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📈 Gradient Boosting<br>• Learning Rate: 0.05<br>• Shallow Trees<br>• Subsample: 0.8</p></span></div></foreignObject></g></g><g transform="translate(1202.8437576293945, 1633)" id="flowchart-XGB-15" class="node default ml"><rect height="126" width="222.08750915527344" y="-63" x="-111.04375457763672" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-81.04375457763672, -48)" style="" class="label"><rect></rect><foreignObject height="96" width="162.08750915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>⚡ XGBoost<br>• L1/L2 Regularization<br>• Feature Subsampling<br>• Early Stopping</p></span></div></foreignObject></g></g><g transform="translate(1461.5625076293945, 1633)" id="flowchart-LGB-16" class="node default ml"><rect height="126" width="195.35000610351562" y="-63" x="-97.67500305175781" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-67.67500305175781, -48)" style="" class="label"><rect></rect><foreignObject height="96" width="135.35000610351562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🚀 LightGBM<br>• Fast Training<br>• Memory Efficient<br>• High Accuracy</p></span></div></foreignObject></g></g><g transform="translate(1190.303134918213, 1859)" id="flowchart-Ridge-17" class="node default ml"><rect height="126" width="226.125" y="-63" x="-113.0625" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-83.0625, -48)" style="" class="label"><rect></rect><foreignObject height="96" width="166.125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📐 Ridge Regression<br>• Alpha: 50.0<br>• Strong Regularization<br>• Combines Predictions</p></span></div></foreignObject></g></g><g transform="translate(1190.303134918213, 2060)" id="flowchart-FinalPred-18" class="node default ml"><rect height="126" width="213.8874969482422" y="-63" x="-106.9437484741211" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-76.9437484741211, -48)" style="" class="label"><rect></rect><foreignObject height="96" width="153.8874969482422"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🎯 Final Prediction<br>• Weight Estimate<br>• Confidence Interval<br>• Error Bounds</p></span></div></foreignObject></g></g><g transform="translate(1190.303134918213, 2286)" id="flowchart-Threshold-19" class="node default decision"><rect height="126" width="206.52500915527344" y="-63" x="-103.26250457763672" style="fill:#fff3e0 !important;stroke:#e65100 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-73.26250457763672, -48)" style="" class="label"><rect></rect><foreignObject height="96" width="146.52500915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🎚️ Threshold Check<br>• Target Weight ± 2g<br>• Tolerance Band<br>• Quality Control</p></span></div></foreignObject></g></g><g transform="translate(927.4906349182129, 2462)" id="flowchart-Accept-20" class="node default decision"><rect height="126" width="204.6374969482422" y="-63" x="-102.3187484741211" style="fill:#fff3e0 !important;stroke:#e65100 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-72.3187484741211, -48)" style="" class="label"><rect></rect><foreignObject height="96" width="144.6374969482422"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>✅ ACCEPT<br>• Within Tolerance<br>• Pass to Next Stage<br>• Log Success</p></span></div></foreignObject></g></g><g transform="translate(1190.303134918213, 2462)" id="flowchart-Reject-21" class="node default decision"><rect height="126" width="202.8125" y="-63" x="-101.40625" style="fill:#fff3e0 !important;stroke:#e65100 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-71.40625, -48)" style="" class="label"><rect></rect><foreignObject height="96" width="142.8125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>❌ REJECT<br>• Outside Tolerance<br>• Remove from Line<br>• Log Failure</p></span></div></foreignObject></g></g><g transform="translate(927.4906349182129, 2688)" id="flowchart-Conveyor-22" class="node default control"><rect height="126" width="210.53750610351562" y="-63" x="-105.26875305175781" style="fill:#fce4ec !important;stroke:#880e4f !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-75.26875305175781, -48)" style="" class="label"><rect></rect><foreignObject height="96" width="150.53750610351562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔄 Conveyor Control<br>• Speed Regulation<br>• Position Tracking<br>• Synchronization</p></span></div></foreignObject></g></g><g transform="translate(1190.303134918213, 2688)" id="flowchart-Pneumatic-23" class="node default control"><rect height="126" width="215.08750915527344" y="-63" x="-107.54375457763672" style="fill:#fce4ec !important;stroke:#880e4f !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-77.54375457763672, -48)" style="" class="label"><rect></rect><foreignObject height="96" width="155.08750915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>💨 Pneumatic Pusher<br>• Reject Mechanism<br>• Fast Response<br>• Precise Timing</p></span></div></foreignObject></g></g><g transform="translate(1190.303134918213, 2864)" id="flowchart-Sorting-24" class="node default control"><rect height="126" width="199.52500915527344" y="-63" x="-99.76250457763672" style="fill:#fce4ec !important;stroke:#880e4f !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-69.76250457763672, -48)" style="" class="label"><rect></rect><foreignObject height="96" width="139.52500915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📦 Package Sorting<br>• Accept Route<br>• Reject Route<br>• Quality Bins</p></span></div></foreignObject></g></g></g></g></g></svg>