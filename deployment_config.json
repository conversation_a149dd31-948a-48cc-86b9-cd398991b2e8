{"model_info": {"name": "Medical Package Weight Prediction System", "version": "1.0", "accuracy": "89.16%", "rmse": "4.523g", "features_count": 215, "training_samples": 576}, "hardware_requirements": {"load_cell": "Bending Beam Load Cell", "adc": "HX711 24-bit ADC", "microcontroller": "Raspberry Pi Pico or STM32", "sampling_rate": "50-100 Hz", "memory_requirement": "~2MB for model storage"}, "performance_metrics": {"accuracy_range": "90.0% - 99.7%", "mean_accuracy": "96.30%", "prediction_tolerance": "±8.76g (95% confidence)", "max_error": "28.70g"}}