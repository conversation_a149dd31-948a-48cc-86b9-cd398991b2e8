{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Enhanced Real-Time Weight Prediction System with Rigorous Overfitting Prevention\n", "\n", "## Project Overview\n", "This notebook implements an enhanced real-time weight prediction system with:\n", "- **Rigorous Cross-Validation**: Enhanced out-of-fold predictions and increased CV folds\n", "- **Advanced Regularization**: Stronger regularization across all model layers\n", "- **Model Diversity**: Diverse base learners with varying hyperparameters\n", "- **Iterative Feature Selection**: Comprehensive feature optimization\n", "- **Confidence Intervals**: Prediction uncertainty quantification\n", "\n", "### Enhanced Features\n", "- **Samples**: 721 medical packages\n", "- **Features**: 1500 → Optimally selected subset\n", "- **Target**: Weight labels from 50g to 100g\n", "- **Goal**: Achieve 90-95% accuracy with minimal overfitting (<3% gap)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Enhanced imports with additional libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.model_selection import (\n", "    train_test_split, GridSearchCV, cross_val_score, \n", "    Stratified<PERSON><PERSON><PERSON>, RepeatedKFold, cross_validate\n", ")\n", "from sklearn.preprocessing import StandardScaler, RobustScaler, MinMaxScaler\n", "from sklearn.feature_selection import (\n", "    SelectKBest, f_regression, RFE, SelectFromModel,\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, mutual_info_regression\n", ")\n", "from sklearn.decomposition import PCA\n", "from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score\n", "from sklearn.ensemble import (\n", "    RandomForestRegressor, GradientBoostingRegressor,\n", "    ExtraTrees<PERSON><PERSON><PERSON>or, BaggingRegressor\n", ")\n", "from sklearn.tree import DecisionTreeRegressor\n", "from sklearn.linear_model import (\n", "    LinearRegression, Ridge, Lasso, ElasticNet,\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON>, HuberRegressor\n", ")\n", "from sklearn.neighbors import KNeighborsRegressor\n", "from sklearn.svm import SVR\n", "from xgboost import XGBRegressor\n", "from lightgbm import LGBMRegressor\n", "import scipy.stats as stats\n", "from scipy import signal\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set random seed for reproducibility\n", "np.random.seed(42)\n", "\n", "print(\"Enhanced libraries imported successfully!\")\n", "print(\"Ready for rigorous overfitting prevention and model optimization\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load and explore the dataset\n", "df = pd.read_csv('Merged_Cleaned.csv')\n", "\n", "print(f\"Dataset shape: {df.shape}\")\n", "print(f\"\\nTarget variable distribution:\")\n", "print(df['Label'].value_counts().sort_index())\n", "\n", "# Check for missing values and data quality\n", "print(f\"\\nMissing values: {df.isnull().sum().sum()}\")\n", "print(f\"Duplicate rows: {df.duplicated().sum()}\")\n", "\n", "# Enhanced statistical analysis\n", "print(f\"\\nTarget statistics:\")\n", "print(df['Label'].describe())\n", "\n", "# Check for class imbalance\n", "label_counts = df['Label'].value_counts().sort_index()\n", "imbalance_ratio = label_counts.max() / label_counts.min()\n", "print(f\"\\nClass imbalance ratio: {imbalance_ratio:.2f}\")\n", "if imbalance_ratio > 3:\n", "    print(\"⚠️  Significant class imbalance detected - will use stratified sampling\")\n", "else:\n", "    print(\"✅ Acceptable class balance\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Enhanced data visualization and analysis\n", "fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n", "\n", "# Target distribution with enhanced statistics\n", "axes[0,0].hist(df['Label'], bins=25, edgecolor='black', alpha=0.7, color='skyblue')\n", "axes[0,0].axvline(df['Label'].mean(), color='red', linestyle='--', label=f'Mean: {df[\"Label\"].mean():.1f}g')\n", "axes[0,0].axvline(df['Label'].median(), color='green', linestyle='--', label=f'Median: {df[\"Label\"].median():.1f}g')\n", "axes[0,0].set_title('Enhanced Weight Distribution Analysis')\n", "axes[0,0].set_xlabel('Weight (grams)')\n", "axes[0,0].set_ylabel('Frequency')\n", "axes[0,0].legend()\n", "axes[0,0].grid(True, alpha=0.3)\n", "\n", "# Box plot with outlier analysis\n", "box_plot = axes[0,1].boxplot(df['Label'], patch_artist=True)\n", "box_plot['boxes'][0].set_facecolor('lightblue')\n", "axes[0,1].set_title('Weight Distribution Box Plot')\n", "axes[0,1].set_ylabel('Weight (grams)')\n", "axes[0,1].grid(True, alpha=0.3)\n", "\n", "# Q-Q plot for normality check\n", "stats.probplot(df['Label'], dist=\"norm\", plot=axes[0,2])\n", "axes[0,2].set_title('Q-Q Plot (Normality Check)')\n", "axes[0,2].grid(True, alpha=0.3)\n", "\n", "# Feature correlation heatmap (sample)\n", "sample_features = ['x1', 'x10', 'x100', 'x500', 'x1000', 'Label']\n", "corr_matrix = df[sample_features].corr()\n", "sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0, ax=axes[1,0])\n", "axes[1,0].set_title('Sample Feature Correlation Matrix')\n", "\n", "# Feature variance analysis\n", "feature_cols = [col for col in df.columns if col.startswith('x') or col.startswith('X')]\n", "feature_vars = df[feature_cols].var().sort_values(ascending=False)\n", "axes[1,1].plot(range(len(feature_vars)), feature_vars.values)\n", "axes[1,1].set_title('Feature Variance Distribution')\n", "axes[1,1].set_xlabel('Feature Index (sorted by variance)')\n", "axes[1,1].set_ylabel('Variance')\n", "axes[1,1].set_yscale('log')\n", "axes[1,1].grid(True, alpha=0.3)\n", "\n", "# Target correlation analysis\n", "correlations = df[feature_cols[:100] + ['Label']].corr()['Label'].abs().sort_values(ascending=False)\n", "axes[1,2].bar(range(20), correlations.head(21)[1:21])  # Top 20 correlations\n", "axes[1,2].set_title('Top 20 Feature Correlations with Target')\n", "axes[1,2].set_xlabel('Feature Rank')\n", "axes[1,2].set_ylabel('Absolute Correlation')\n", "axes[1,2].grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(f\"\\nTop 10 features correlated with target:\")\n", "print(correlations.head(11)[1:])  # Exclude self-correlation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Enhanced data preparation with multiple splitting strategies\n", "X = df.drop('Label', axis=1)\n", "y = df['Label']\n", "\n", "print(f\"Features shape: {X.shape}\")\n", "print(f\"Target shape: {y.shape}\")\n", "\n", "# Enhanced train-test split with stratification\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X, y, test_size=0.2, random_state=42, stratify=y\n", ")\n", "\n", "# Additional validation split for hyperparameter tuning\n", "X_train_sub, X_val, y_train_sub, y_val = train_test_split(\n", "    X_train, y_train, test_size=0.2, random_state=42, stratify=y_train\n", ")\n", "\n", "print(f\"\\nEnhanced data splits:\")\n", "print(f\"Training set: {X_train.shape} (Full training)\")\n", "print(f\"Training subset: {X_train_sub.shape} (For hyperparameter tuning)\")\n", "print(f\"Validation set: {X_val.shape} (For hyperparameter validation)\")\n", "print(f\"Test set: {X_test.shape} (Final evaluation)\")\n", "\n", "print(f\"\\nTarget distribution in splits:\")\n", "print(f\"Train: {y_train.value_counts().sort_index().to_dict()}\")\n", "print(f\"Test: {y_test.value_counts().sort_index().to_dict()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Enhanced Digital Signal Processing Pipeline\n", "\n", "Advanced feature engineering with multiple DSP techniques and comprehensive feature selection."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Enhanced DSP-inspired feature engineering\n", "def extract_enhanced_dsp_features(X):\n", "    \"\"\"\n", "    Extract comprehensive time-domain and frequency-domain features\n", "    Enhanced version with additional statistical and signal processing features\n", "    \"\"\"\n", "    features = []\n", "    \n", "    for idx, row in X.iterrows():\n", "        signal = row.values\n", "        \n", "        # Enhanced time-domain features\n", "        mean_val = np.mean(signal)\n", "        std_val = np.std(signal)\n", "        var_val = np.var(signal)\n", "        rms = np.sqrt(np.mean(signal**2))\n", "        peak_to_peak = np.max(signal) - np.min(signal)\n", "        \n", "        # Robust statistical measures\n", "        median_val = np.median(signal)\n", "        mad = np.median(np.abs(signal - median_val))  # Median Absolute Deviation\n", "        iqr = np.percentile(signal, 75) - np.percentile(signal, 25)\n", "        \n", "        # Higher-order moments\n", "        skewness = stats.skew(signal) if std_val > 0 else 0\n", "        kurtosis = stats.kurtosis(signal) if std_val > 0 else 0\n", "        \n", "        # Signal energy and power\n", "        energy = np.sum(signal**2)\n", "        power = energy / len(signal)\n", "        \n", "        # Zero crossing rate\n", "        zero_crossings = np.sum(np.diff(np.sign(signal - mean_val)) != 0)\n", "        \n", "        # Enhanced moving average features\n", "        window_sizes = [5, 10, 25, 50, 100]\n", "        ma_features = []\n", "        for window in window_sizes:\n", "            if len(signal) >= window:\n", "                ma = np.convolve(signal, np.ones(window)/window, mode='valid')\n", "                ma_features.extend([np.mean(ma), np.std(ma), np.max(ma) - np.min(ma)])\n", "            else:\n", "                ma_features.extend([mean_val, std_val, peak_to_peak])\n", "        \n", "        # Enhanced frequency-domain features\n", "        fft = np.fft.fft(signal)\n", "        fft_magnitude = np.abs(fft[:len(fft)//2])\n", "        fft_phase = np.angle(fft[:len(fft)//2])\n", "        \n", "        # Spectral features\n", "        dominant_freq_idx = np.argmax(fft_magnitude[1:]) + 1\n", "        dominant_freq_magnitude = fft_magnitude[dominant_freq_idx]\n", "        spectral_energy = np.sum(fft_magnitude**2)\n", "        spectral_centroid = np.sum(np.arange(len(fft_magnitude)) * fft_magnitude) / np.sum(fft_magnitude)\n", "        spectral_rolloff = np.where(np.cumsum(fft_magnitude) >= 0.85 * np.sum(fft_magnitude))[0][0]\n", "        spectral_bandwidth = np.sqrt(np.sum(((np.arange(len(fft_magnitude)) - spectral_centroid) ** 2) * fft_magnitude) / np.sum(fft_magnitude))\n", "        \n", "        # Spectral flux (measure of spectral change)\n", "        spectral_flux = np.sum(np.diff(fft_magnitude)**2)\n", "        \n", "        # Combine all features\n", "        row_features = [\n", "            # Basic statistics\n", "            mean_val, median_val, std_val, var_val, mad, iqr,\n", "            # Signal characteristics\n", "            rms, peak_to_peak, energy, power, zero_crossings,\n", "            # Higher-order moments\n", "            skewness, kurtosis,\n", "            # Moving averages (15 features: 5 windows × 3 stats)\n", "            *ma_features,\n", "            # Spectral features\n", "            dominant_freq_magnitude, spectral_energy, spectral_centroid,\n", "            spectral_rolloff, spectral_bandwidth, spectral_flux\n", "        ]\n", "        \n", "        features.append(row_features)\n", "    \n", "    feature_names = [\n", "        'mean', 'median', 'std', 'var', 'mad', 'iqr',\n", "        'rms', 'peak_to_peak', 'energy', 'power', 'zero_crossings',\n", "        'skewness', 'kurtosis'\n", "    ]\n", "    \n", "    # Add moving average feature names\n", "    for window in [5, 10, 25, 50, 100]:\n", "        feature_names.extend([f'ma{window}_mean', f'ma{window}_std', f'ma{window}_range'])\n", "    \n", "    # Add spectral feature names\n", "    feature_names.extend([\n", "        'dominant_freq_mag', 'spectral_energy', 'spectral_centroid',\n", "        'spectral_rolloff', 'spectral_bandwidth', 'spectral_flux'\n", "    ])\n", "    \n", "    return pd.DataFrame(features, columns=feature_names, index=X.index)\n", "\n", "print(\"Extracting enhanced DSP features...\")\n", "X_train_dsp = extract_enhanced_dsp_features(X_train)\n", "X_test_dsp = extract_enhanced_dsp_features(X_test)\n", "\n", "print(f\"Enhanced DSP features shape: {X_train_dsp.shape}\")\n", "print(f\"DSP feature names: {X_train_dsp.columns.tolist()[:10]}...\")  # Show first 10\n", "print(f\"Total DSP features: {len(X_train_dsp.columns)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Comprehensive Feature Selection and Scaling Optimization\n", "\n", "Iterative feature selection with multiple methods and optimal scaler selection."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Comprehensive feature selection with iterative optimization\n", "def comprehensive_feature_selection(X_train, X_test, X_train_dsp, X_test_dsp, y_train):\n", "    \"\"\"\n", "    Comprehensive feature selection using multiple methods and iterative optimization\n", "    \"\"\"\n", "    from sklearn.feature_selection import (\n", "        SelectKBest, f_regression, RFE, SelectFromModel,\n", "        <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, mutual_info_regression\n", "    )\n", "    from sklearn.ensemble import RandomForestRegressor\n", "    from sklearn.linear_model import LassoCV\n", "    from sklearn.decomposition import PCA\n", "    \n", "    feature_cols = [col for col in X_train.columns if col.startswith('x') or col.startswith('X')]\n", "    print(f\"Starting with {len(feature_cols)} original features\")\n", "    \n", "    # Step 1: Remove low-variance features\n", "    variance_selector = VarianceThreshold(threshold=0.01)\n", "    X_train_var = variance_selector.fit_transform(X_train[feature_cols])\n", "    X_test_var = variance_selector.transform(X_test[feature_cols])\n", "    selected_features_var = np.array(feature_cols)[variance_selector.get_support()]\n", "    print(f\"After variance threshold: {X_train_var.shape[1]} features\")\n", "    \n", "    # Step 2: Statistical feature selection (F-test)\n", "    feature_numbers = [50, 100, 150, 200, 250]\n", "    best_f_score = -np.inf\n", "    best_f_features = 100\n", "    \n", "    for n_features in feature_numbers:\n", "        if n_features <= X_train_var.shape[1]:\n", "            selector_f = SelectKBest(score_func=f_regression, k=n_features)\n", "            X_temp = selector_f.fit_transform(X_train_var, y_train)\n", "            \n", "            # Quick validation with simple model\n", "            rf_temp = RandomForestRegressor(n_estimators=50, random_state=42)\n", "            scores = cross_val_score(rf_temp, X_temp, y_train, cv=5, scoring='r2')\n", "            avg_score = np.mean(scores)\n", "            \n", "            if avg_score > best_f_score:\n", "                best_f_score = avg_score\n", "                best_f_features = n_features\n", "    \n", "    print(f\"Best F-test features: {best_f_features} (R² = {best_f_score:.4f})\")\n", "    \n", "    # Apply best F-test selection\n", "    selector_f = SelectKBest(score_func=f_regression, k=best_f_features)\n", "    X_train_f = selector_f.fit_transform(X_train_var, y_train)\n", "    X_test_f = selector_f.transform(X_test_var)\n", "    \n", "    # Step 3: Mutual Information selection\n", "    mi_selector = SelectKBest(score_func=mutual_info_regression, k=min(50, X_train_f.shape[1]))\n", "    X_train_mi = mi_selector.fit_transform(X_train_f, y_train)\n", "    X_test_mi = mi_selector.transform(X_test_f)\n", "    print(f\"After mutual information: {X_train_mi.shape[1]} features\")\n", "    \n", "    # Step 4: Recursive Feature Elimination with cross-validation\n", "    rf_selector = RandomForestRegressor(n_estimators=100, max_depth=10, random_state=42)\n", "    rfe_features = min(30, X_train_mi.shape[1])\n", "    rfe = RFE(estimator=rf_selector, n_features_to_select=rfe_features, step=5)\n", "    X_train_rfe = rfe.fit_transform(X_train_mi, y_train)\n", "    X_test_rfe = rfe.transform(X_test_mi)\n", "    print(f\"After RFE: {X_train_rfe.shape[1]} features\")\n", "    \n", "    # Step 5: Lasso-based feature selection\n", "    lasso_selector = SelectFromModel(LassoCV(cv=5, random_state=42))\n", "    X_train_lasso = lasso_selector.fit_transform(X_train_rfe, y_train)\n", "    X_test_lasso = lasso_selector.transform(X_test_rfe)\n", "    print(f\"After Lasso selection: {X_train_lasso.shape[1]} features\")\n", "    \n", "    # Step 6: PCA for dimensionality reduction\n", "    # Find optimal number of components\n", "    pca_temp = PCA(random_state=42)\n", "    pca_temp.fit(X_train_lasso)\n", "    cumsum_var = np.cumsum(pca_temp.explained_variance_ratio_)\n", "    n_components = np.argmax(cumsum_var >= 0.95) + 1  # 95% variance explained\n", "    n_components = min(n_components, 25)  # Cap at 25 components\n", "    \n", "    pca = PCA(n_components=n_components, random_state=42)\n", "    X_train_pca = pca.fit_transform(X_train_lasso)\n", "    X_test_pca = pca.transform(X_test_lasso)\n", "    print(f\"PCA components: {n_components} (explaining {cumsum_var[n_components-1]:.3f} variance)\")\n", "    \n", "    # Combine selected features with DSP features\n", "    X_train_combined = np.hstack([X_train_lasso, X_train_pca, X_train_dsp.values])\n", "    X_test_combined = np.hstack([X_test_lasso, X_test_pca, X_test_dsp.values])\n", "    \n", "    # Create feature names\n", "    feature_names = (\n", "        [f'selected_{i}' for i in range(X_train_lasso.shape[1])] +\n", "        [f'pca_{i}' for i in range(X_train_pca.shape[1])] +\n", "        X_train_dsp.columns.tolist()\n", "    )\n", "    \n", "    print(f\"\\nFinal feature set: {X_train_combined.shape[1]} features\")\n", "    print(f\"Reduction: {len(feature_cols)} → {X_train_combined.shape[1]} ({X_train_combined.shape[1]/len(feature_cols)*100:.1f}%)\")\n", "    \n", "    return X_train_combined, X_test_combined, feature_names\n", "\n", "# Apply comprehensive feature selection\n", "print(\"Applying comprehensive feature selection...\")\n", "X_train_selected, X_test_selected, selected_feature_names = comprehensive_feature_selection(\n", "    X_train, X_test, X_train_dsp, X_test_dsp, y_train\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Comprehensive scaler comparison and selection\n", "def compare_scalers(X_train, X_test, y_train):\n", "    \"\"\"\n", "    Compare different scaling methods and select the best one\n", "    \"\"\"\n", "    scalers = {\n", "        'StandardScaler': StandardScaler(),\n", "        'RobustScaler': RobustScaler(),\n", "        'MinMaxScaler': MinMaxScaler()\n", "    }\n", "    \n", "    scaler_results = {}\n", "    \n", "    # Quick model for evaluation\n", "    eval_model = RandomForestRegressor(n_estimators=100, random_state=42)\n", "    \n", "    for name, scaler in scalers.items():\n", "        # Scale the data\n", "        X_train_scaled = scaler.fit_transform(X_train)\n", "        \n", "        # Cross-validation\n", "        cv_scores = cross_val_score(eval_model, X_train_scaled, y_train, cv=5, scoring='r2')\n", "        \n", "        scaler_results[name] = {\n", "            'mean_r2': np.mean(cv_scores),\n", "            'std_r2': np.std(cv_scores),\n", "            'scaler': scaler\n", "        }\n", "        \n", "        print(f\"{name}: R² = {np.mean(cv_scores):.4f} ± {np.std(cv_scores):.4f}\")\n", "    \n", "    # Select best scaler\n", "    best_scaler_name = max(scaler_results.keys(), key=lambda k: scaler_results[k]['mean_r2'])\n", "    best_scaler = scaler_results[best_scaler_name]['scaler']\n", "    \n", "    print(f\"\\n🏆 Best scaler: {best_scaler_name}\")\n", "    \n", "    # Apply best scaler\n", "    X_train_scaled = best_scaler.fit_transform(X_train)\n", "    X_test_scaled = best_scaler.transform(X_test)\n", "    \n", "    return X_train_scaled, X_test_scaled, best_scaler, best_scaler_name\n", "\n", "# Compare and select best scaler\n", "print(\"Comparing scaling methods...\")\n", "X_train_scaled, X_test_scaled, best_scaler, best_scaler_name = compare_scalers(\n", "    X_train_selected, X_test_selected, y_train\n", ")\n", "\n", "print(f\"\\nFinal scaled feature set: {X_train_scaled.shape}\")\n", "print(f\"Selected scaler: {best_scaler_name}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Enhanced Stacking Ensemble with Rigorous Cross-Validation\n", "\n", "Implementation of diverse base models with enhanced regularization and rigorous out-of-fold predictions."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Enhanced diverse base models with varying hyperparameters\n", "def get_diverse_base_models():\n", "    \"\"\"\n", "    Create diverse base models with different algorithms and hyperparameters\n", "    Enhanced diversity for better stacking performance\n", "    \"\"\"\n", "    models = {\n", "        # Tree-based models with different configurations\n", "        'decision_tree_shallow': DecisionTreeRegressor(\n", "            max_depth=6, min_samples_split=50, min_samples_leaf=25,\n", "            max_features='sqrt', random_state=42\n", "        ),\n", "        'decision_tree_deep': DecisionTreeRegressor(\n", "            max_depth=12, min_samples_split=30, min_samples_leaf=15,\n", "            max_features='log2', random_state=43\n", "        ),\n", "        \n", "        # Random Forest variants\n", "        'random_forest_conservative': RandomForestRegressor(\n", "            n_estimators=200, max_depth=10, min_samples_split=40,\n", "            min_samples_leaf=20, max_features='sqrt', bootstrap=True,\n", "            oob_score=True, random_state=42, n_jobs=-1\n", "        ),\n", "        'random_forest_aggressive': RandomForestRegressor(\n", "            n_estimators=300, max_depth=15, min_samples_split=20,\n", "            min_samples_leaf=10, max_features='log2', bootstrap=True,\n", "            oob_score=True, random_state=43, n_jobs=-1\n", "        ),\n", "        \n", "        # Extra Trees for additional diversity\n", "        'extra_trees': ExtraTreesRegressor(\n", "            n_estimators=200, max_depth=12, min_samples_split=30,\n", "            min_samples_leaf=15, max_features='sqrt', bootstrap=False,\n", "            random_state=42, n_jobs=-1\n", "        ),\n", "        \n", "        # Gradient Boosting variants\n", "        'gradient_boosting_slow': GradientBoostingRegressor(\n", "            n_estimators=200, learning_rate=0.03, max_depth=4,\n", "            min_samples_split=50, min_samples_leaf=25, subsample=0.8,\n", "            max_features='sqrt', random_state=42\n", "        ),\n", "        'gradient_boosting_fast': GradientBoostingRegressor(\n", "            n_estimators=100, learning_rate=0.1, max_depth=6,\n", "            min_samples_split=30, min_samples_leaf=15, subsample=0.9,\n", "            max_features='log2', random_state=43\n", "        ),\n", "        \n", "        # XGBoost variants\n", "        'xgboost_regularized': XGBRegressor(\n", "            n_estimators=200, learning_rate=0.03, max_depth=4,\n", "            min_child_weight=15, subsample=0.8, colsample_bytree=0.8,\n", "            reg_alpha=0.3, reg_lambda=1.5, random_state=42, n_jobs=-1\n", "        ),\n", "        'xgboost_balanced': XGBRegressor(\n", "            n_estimators=150, learning_rate=0.05, max_depth=6,\n", "            min_child_weight=10, subsample=0.9, colsample_bytree=0.9,\n", "            reg_alpha=0.1, reg_lambda=1.0, random_state=43, n_jobs=-1\n", "        ),\n", "        \n", "        # LightGBM variants\n", "        'lightgbm_conservative': LGBMRegressor(\n", "            n_estimators=200, learning_rate=0.03, max_depth=4,\n", "            min_child_samples=40, subsample=0.8, colsample_bytree=0.8,\n", "            reg_alpha=0.3, reg_lambda=1.5, random_state=42, n_jobs=-1, verbose=-1\n", "        ),\n", "        'lightgbm_fast': LGBMRegressor(\n", "            n_estimators=100, learning_rate=0.08, max_depth=6,\n", "            min_child_samples=25, subsample=0.9, colsample_bytree=0.9,\n", "            reg_alpha=0.1, reg_lambda=1.0, random_state=43, n_jobs=-1, verbose=-1\n", "        ),\n", "        \n", "        # Linear models for diversity\n", "        'linear_regression': LinearRegression(),\n", "        'ridge_regression': Ridge(alpha=10.0, random_state=42),\n", "        'lasso_regression': <PERSON><PERSON>(alpha=1.0, random_state=42),\n", "        'elastic_net': ElasticNet(alpha=1.0, l1_ratio=0.5, random_state=42),\n", "        'bayesian_ridge': <PERSON><PERSON><PERSON><PERSON><PERSON>(),\n", "        'huber_regressor': <PERSON><PERSON>Regressor(epsilon=1.35),\n", "        \n", "        # Non-parametric models\n", "        'knn_uniform': KNeighborsRegressor(n_neighbors=7, weights='uniform'),\n", "        'knn_distance': KNeighborsRegressor(n_neighbors=5, weights='distance'),\n", "        \n", "        # Support Vector Regression\n", "        'svr_rbf': SVR(kernel='rbf', C=1.0, gamma='scale', epsilon=0.1),\n", "        'svr_linear': SVR(kernel='linear', C=1.0, epsilon=0.1)\n", "    }\n", "    \n", "    return models\n", "\n", "# Initialize diverse base models\n", "base_models = get_diverse_base_models()\n", "print(f\"Initialized {len(base_models)} diverse base models:\")\n", "for name in base_models.keys():\n", "    print(f\"  - {name}\")\n", "print(\"\\nEnhanced model diversity for robust stacking ensemble\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Enhanced Stacking Ensemble with Rigorous Cross-Validation\n", "class EnhancedStackingEnsemble:\n", "    def __init__(self, base_models, meta_models=None, cv_folds=15, random_state=42):\n", "        self.base_models = base_models\n", "        self.meta_models = meta_models if meta_models else self._get_meta_models()\n", "        self.cv_folds = cv_folds\n", "        self.random_state = random_state\n", "        self.trained_base_models = {}\n", "        self.best_meta_model = None\n", "        self.meta_model_name = None\n", "        self.feature_importance_ = None\n", "        \n", "    def _get_meta_models(self):\n", "        \"\"\"Define multiple meta-learner candidates\"\"\"\n", "        return {\n", "            'ridge_conservative': Ridge(alpha=100.0, random_state=self.random_state),\n", "            'ridge_moderate': Ridge(alpha=50.0, random_state=self.random_state),\n", "            'ridge_aggressive': Ridge(alpha=10.0, random_state=self.random_state),\n", "            'lasso': <PERSON><PERSON>(alpha=1.0, random_state=self.random_state),\n", "            'elastic_net': ElasticNet(alpha=1.0, l1_ratio=0.5, random_state=self.random_state),\n", "            'bayesian_ridge': <PERSON><PERSON><PERSON><PERSON><PERSON>(),\n", "            'huber': <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(epsilon=1.35),\n", "            'linear': LinearRegression()\n", "        }\n", "    \n", "    def fit(self, X, y):\n", "        \"\"\"Enhanced training with rigorous cross-validation\"\"\"\n", "        print(f\"Training enhanced stacking ensemble with {self.cv_folds}-fold CV...\")\n", "        \n", "        # Step 1: Generate out-of-fold predictions with enhanced CV\n", "        kfold = RepeatedKFold(n_splits=self.cv_folds, n_repeats=2, random_state=self.random_state)\n", "        meta_features = np.zeros((X.shape[0], len(self.base_models)))\n", "        \n", "        print(\"Generating out-of-fold predictions...\")\n", "        fold_scores = {name: [] for name in self.base_models.keys()}\n", "        \n", "        for fold_idx, (train_idx, val_idx) in enumerate(kfold.split(X, y)):\n", "            if fold_idx % 5 == 0:\n", "                print(f\"  Processing fold {fold_idx + 1}/{self.cv_folds * 2}...\")\n", "            \n", "            X_fold_train, X_fold_val = X[train_idx], X[val_idx]\n", "            y_fold_train, y_fold_val = y.iloc[train_idx], y.iloc[val_idx]\n", "            \n", "            for i, (name, model) in enumerate(self.base_models.items()):\n", "                try:\n", "                    # Clone and train model on fold\n", "                    fold_model = model.__class__(**model.get_params())\n", "                    fold_model.fit(X_fold_train, y_fold_train)\n", "                    \n", "                    # Predict on validation set\n", "                    val_pred = fold_model.predict(X_fold_val)\n", "                    meta_features[val_idx, i] = val_pred\n", "                    \n", "                    # Track fold performance\n", "                    fold_r2 = r2_score(y_fold_val, val_pred)\n", "                    fold_scores[name].append(fold_r2)\n", "                    \n", "                except Exception as e:\n", "                    print(f\"Warning: Model {name} failed on fold {fold_idx}: {e}\")\n", "                    # Use mean prediction as fallback\n", "                    meta_features[val_idx, i] = y_fold_train.mean()\n", "        \n", "        # Step 2: Evaluate base model performance\n", "        print(\"\\nBase model cross-validation performance:\")\n", "        base_performance = {}\n", "        for name, scores in fold_scores.items():\n", "            if scores:  # Only if we have valid scores\n", "                mean_score = np.mean(scores)\n", "                std_score = np.std(scores)\n", "                base_performance[name] = mean_score\n", "                print(f\"  {name}: R² = {mean_score:.4f} ± {std_score:.4f}\")\n", "        \n", "        # Step 3: Select best base models (ensemble pruning)\n", "        sorted_models = sorted(base_performance.items(), key=lambda x: x[1], reverse=True)\n", "        top_models = dict(sorted_models[:15])  # Keep top 15 models\n", "        \n", "        print(f\"\\nSelected top {len(top_models)} base models for final ensemble\")\n", "        \n", "        # Update meta_features to include only top models\n", "        selected_indices = [i for i, name in enumerate(self.base_models.keys()) if name in top_models]\n", "        meta_features_selected = meta_features[:, selected_indices]\n", "        \n", "        # Step 4: Train base models on full dataset\n", "        print(\"Training selected base models on full dataset...\")\n", "        self.trained_base_models = {}\n", "        for name in top_models.keys():\n", "            model = self.base_models[name]\n", "            model.fit(X, y)\n", "            self.trained_base_models[name] = model\n", "        \n", "        # Step 5: Select best meta-learner\n", "        print(\"Selecting optimal meta-learner...\")\n", "        best_meta_score = -np.inf\n", "        \n", "        for meta_name, meta_model in self.meta_models.items():\n", "            # Cross-validate meta-learner\n", "            meta_scores = cross_val_score(\n", "                meta_model, meta_features_selected, y, \n", "                cv=10, scoring='r2'\n", "            )\n", "            mean_meta_score = np.mean(meta_scores)\n", "            \n", "            print(f\"  {meta_name}: R² = {mean_meta_score:.4f} ± {np.std(meta_scores):.4f}\")\n", "            \n", "            if mean_meta_score > best_meta_score:\n", "                best_meta_score = mean_meta_score\n", "                self.best_meta_model = meta_model\n", "                self.meta_model_name = meta_name\n", "        \n", "        # Step 6: Train best meta-learner\n", "        print(f\"\\n🏆 Best meta-learner: {self.meta_model_name} (R² = {best_meta_score:.4f})\")\n", "        self.best_meta_model.fit(meta_features_selected, y)\n", "        \n", "        # Store feature importance if available\n", "        if hasattr(self.best_meta_model, 'coef_'):\n", "            self.feature_importance_ = np.abs(self.best_meta_model.coef_)\n", "        \n", "        return self\n", "    \n", "    def predict(self, X):\n", "        \"\"\"Make predictions using the trained ensemble\"\"\"\n", "        # Get predictions from selected base models\n", "        base_predictions = np.zeros((X.shape[0], len(self.trained_base_models)))\n", "        \n", "        for i, (name, model) in enumerate(self.trained_base_models.items()):\n", "            base_predictions[:, i] = model.predict(X)\n", "        \n", "        # Use meta-learner to combine predictions\n", "        final_predictions = self.best_meta_model.predict(base_predictions)\n", "        \n", "        return final_predictions\n", "    \n", "    def predict_with_confidence(self, X, confidence_level=0.95):\n", "        \"\"\"Make predictions with confidence intervals\"\"\"\n", "        # Get base predictions\n", "        base_predictions = np.zeros((X.shape[0], len(self.trained_base_models)))\n", "        \n", "        for i, (name, model) in enumerate(self.trained_base_models.items()):\n", "            base_predictions[:, i] = model.predict(X)\n", "        \n", "        # Final predictions\n", "        final_predictions = self.best_meta_model.predict(base_predictions)\n", "        \n", "        # Calculate prediction uncertainty based on base model disagreement\n", "        base_std = np.std(base_predictions, axis=1)\n", "        \n", "        # Confidence intervals (simplified approach)\n", "        z_score = stats.norm.ppf((1 + confidence_level) / 2)\n", "        confidence_intervals = z_score * base_std\n", "        \n", "        return final_predictions, confidence_intervals\n", "    \n", "    def get_model_weights(self):\n", "        \"\"\"Get the importance/weights of base models\"\"\"\n", "        if self.feature_importance_ is not None:\n", "            model_names = list(self.trained_base_models.keys())\n", "            return dict(zip(model_names, self.feature_importance_))\n", "        return None\n", "\n", "print(\"Enhanced Stacking Ensemble class defined successfully!\")\n", "print(\"Features: Rigorous CV, Model Pruning, Meta-learner Selection, Confidence Intervals\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}