{"cells": [{"cell_type": "code", "execution_count": 3, "id": "bed11ffd-e715-4d0d-b083-5046d0488f88", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dataset loaded. X shape: (721, 1499), y shape: (721,)\n", "Starting RandomizedSearchCV...\n", "Fitting 3 folds for each of 30 candidates, totalling 90 fits\n", "\n", "RandomizedSearchCV complete.\n", "Best parameters found:  {'stacker__xgb__n_estimators': 200, 'stacker__xgb__max_depth': 5, 'stacker__xgb__learning_rate': 0.2, 'stacker__rf__n_estimators': 100, 'stacker__rf__max_depth': None, 'stacker__final_estimator__fit_intercept': False, 'preprocessor__pca__n_components': 50}\n", "Best negative MSE (CV):  -0.006951756179026423\n", "\n", "Final Model Evaluation on Test Set:\n", "Mean Squared Error (MSE): 0.0000\n", "R-squared (R²): 0.2572\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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********************************/72s77/vqrmzzd9+OZr21a64w7p1lvN0JbwjoyMZEku9e/f34vbPOq1bQFlHf8GL5z395VLkuWlbZXd/e5NBGCUCampqcrMPK6+fT9WZGR8kbZx9GiAdu4M1datAUpNjdBTT50ahzg+Xrr9dum22wq++hYXLyvriCRLXbu+rQYNOp5v9XPaunWulix5XllZWV6pDXAC/g1euOLYV+z3kkUARpkSGRl/wb0t5OSYUXi2bzfTn1tPNGp0XLffXkF9+5p2vXRfVjKqVKl/0T1mpKY64+dXoDjwb/DCeXNfsd9LFgEYjpGba3pr2LnTDC+ZlHSqPa9kAm5srBQTs1srVnTWtGn/UevWdF0GAEBZQwBGmZWTYwaj2LXLTHv2mFF0ThcaKtWrZ6a6dU3fh8nJB7RixS57igYAAMWOAIwy4cQJl6TW2rQpQv/9rwm+Bw6YMdVPFxxszvLGxkr165thIWnaAACAsxCAvWzBgiqSbtPvv4cpLc10lVXQFBBgpsBAc1uu3Hk3DZlAu3ev9OuvntPGjS0krdHy5Z7rV658KvDWrm3GPSfwAgDgbARgL3vxxdqSPtGiRYV7Xrlyp8JwYKCZ/PzqSnpfb75ZXU2bmsEWqlY1U/58lSplLzxblrkgbccOaevWU9Nvv5nbtLSCnuUn6ZBq1CivunVDVL26GfGmcuUSLh4AAPg8ArCXtWuXoe+/X62YmPYqX76y8vJ0xpSba9qn5uRIJ0+a5+XmSsePm+mUMEn36cMPz/56LpcJwfnBuGpVKTzcc6pS5cxlYWElH5zz8qQjR8wIM6ePRZ6SIu3ebaakJHN7rh5c/Pykhg2l5s1N7wymh4YNuuGGS3TNNWsu+ipaAABQthGAvWzChO1q0yZB1113YUHs9DCcnX3qNitL2rcvScuXv6877hgqP79qOnhQOnjQnB09eNCcCbUs6dAhM23dWrhaQ0KkSpXMUIgVK0oVKpw5HxhoQrafn7k9fd7Pz2zn9JrzbzMzTX3p6eY2LU06evTMNrnnEhNj+txt0MAE3vz5+vXNxWqnW7s2p3BvHgAAOFapCMATJ07Uq6++qn379qlFixZ666231L59e7vL8opy5cyFWcHBZz4WHp6q5ctf0pNP3qjWraud8fiJEyb4nh6KDx6UDh8+FYrzp9OXHf3fADHp6WYqaWFhUlSU51SzpmmjW7u2VKuWab7ghVEhAQAAzuDzAfjTTz/VsGHD9O6776pDhw6aMGGCevbsqS1btigqKsru8mzl7y9Vq2amwjhxwgTiw4elY8fMdPx4wbfZ2easrWWZJgwFzee3Ww4KOtV+OTjYdDEWGmrONOffhoeb9QEAAOzi8wH4jTfe0H333adBgwZJkt599119/fXX+uCDD/Tss8/aXF3p5O9/6swrAACA0/h0AM7JydGaNWs0YsQI9zI/Pz8lJCRoxYoVBT4nOztb2dnZ7vtp/+syIL2EfuvPyMiQJO3du0Y5ORkXta3U1C2SpDVr1ri3ezH8/PyUl5fnM9vx5ra2bDH7ytf2u7fqOnAg8X+*********************************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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Cross-validation R² scores: [-0.0190978   0.86465848  0.71167093  0.91683966 -0.00557165]\n", "Mean CV R²: 0.49369992267319346\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "import numpy as np\n", "from sklearn.model_selection import train_test_split, RandomizedSearchCV, cross_val_score\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.decomposition import PCA\n", "from sklearn.ensemble import RandomForestRegressor, StackingRegressor\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.metrics import mean_squared_error, r2_score\n", "from sklearn.impute import SimpleImputer\n", "import xgboost as xgb\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# --------------------------\n", "# Load dataset\n", "# --------------------------\n", "df = pd.read_csv('Merged_Cleaned.csv', index_col=0)\n", "\n", "# Split features and labels\n", "X = df.iloc[:, 1:]\n", "y = df.iloc[:, 0]\n", "print(f\"Dataset loaded. X shape: {X.shape}, y shape: {y.shape}\")\n", "\n", "# --------------------------\n", "# Train-test split\n", "# --------------------------\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X, y, test_size=0.2, random_state=42\n", ")\n", "\n", "# --------------------------\n", "# Preprocessing pipeline\n", "# --------------------------\n", "preprocessing_pipeline = Pipeline([\n", "    ('imputer', SimpleImputer(strategy='mean')),\n", "    ('scaler', StandardScaler()),\n", "    ('pca', PCA())\n", "])\n", "\n", "# --------------------------\n", "# Base models\n", "# --------------------------\n", "base_models = [\n", "    ('rf', RandomForestRegressor(random_state=42, n_jobs=-1)),\n", "    ('xgb', xgb.XGBRegressor(\n", "        random_state=42,\n", "        tree_method='hist',\n", "        n_jobs=-1\n", "    ))\n", "]\n", "\n", "# Meta-learner\n", "meta_learner = LinearRegression()\n", "\n", "# Stacking regressor\n", "stacking_regressor = StackingRegressor(\n", "    estimators=base_models,\n", "    final_estimator=meta_learner,\n", "    cv=5,\n", "    n_jobs=-1\n", ")\n", "\n", "# --------------------------\n", "# Full pipeline\n", "# --------------------------\n", "full_pipeline = Pipeline([\n", "    ('preprocessor', preprocessing_pipeline),\n", "    ('stacker', stacking_regressor)\n", "])\n", "\n", "# --------------------------\n", "# Optimized parameter grid\n", "# --------------------------\n", "param_dist = {\n", "    'preprocessor__pca__n_components': [25, 50, 100, 200],\n", "    'stacker__rf__n_estimators': [100, 200],\n", "    'stacker__rf__max_depth': [5, 10, None],\n", "    'stacker__xgb__n_estimators': [100, 200],\n", "    'stacker__xgb__max_depth': [3, 5, 7],\n", "    'stacker__xgb__learning_rate': [0.05, 0.1, 0.2],\n", "    'stacker__final_estimator__fit_intercept': [True, False]\n", "}\n", "\n", "# --------------------------\n", "# RandomizedSearchCV\n", "# --------------------------\n", "n_iter_search = 30\n", "random_search = RandomizedSearchCV(\n", "    full_pipeline,\n", "    param_distributions=param_dist,\n", "    n_iter=n_iter_search,\n", "    cv=3,\n", "    scoring='neg_mean_squared_error',\n", "    verbose=2,\n", "    random_state=42,\n", "    n_jobs=-1\n", ")\n", "\n", "print(\"Starting RandomizedSearchCV...\")\n", "random_search.fit(X_train, y_train)\n", "print(\"\\nRandomizedSearchCV complete.\")\n", "print(\"Best parameters found: \", random_search.best_params_)\n", "print(\"Best negative MSE (CV): \", random_search.best_score_)\n", "\n", "# --------------------------\n", "# Best model evaluation\n", "# --------------------------\n", "best_model = random_search.best_estimator_\n", "y_pred = best_model.predict(X_test)\n", "\n", "mse = mean_squared_error(y_test, y_pred)\n", "r2 = r2_score(y_test, y_pred)\n", "\n", "print(f\"\\nFinal Model Evaluation on Test Set:\")\n", "print(f\"Mean Squared Error (MSE): {mse:.4f}\")\n", "print(f\"R-squared (R²): {r2:.4f}\")\n", "\n", "# --------------------------\n", "# Visualization\n", "# --------------------------\n", "\n", "# 1. Actual vs Predicted\n", "plt.figure(figsize=(8, 6))\n", "sns.scatterplot(x=y_test, y=y_pred, alpha=0.7)\n", "plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--')\n", "plt.xlabel(\"Actual Values\")\n", "plt.ylabel(\"Predicted Values\")\n", "plt.title(\"Actual vs Predicted\")\n", "plt.grid(True)\n", "plt.show()\n", "\n", "# 2. Residual plot\n", "residuals = y_test - y_pred\n", "plt.figure(figsize=(8, 6))\n", "sns.histplot(residuals, bins=30, kde=True, color=\"blue\")\n", "plt.title(\"Residual Distribution\")\n", "plt.xlabel(\"Residuals\")\n", "plt.show()\n", "\n", "plt.figure(figsize=(8, 6))\n", "sns.scatterplot(x=y_pred, y=residuals, alpha=0.7)\n", "plt.axhline(0, color='red', linestyle='--')\n", "plt.xlabel(\"Predicted Values\")\n", "plt.ylabel(\"Residuals\")\n", "plt.title(\"Residuals vs Predicted\")\n", "plt.show()\n", "\n", "# 3. Cross-validation R² scores\n", "cv_scores = cross_val_score(best_model, X, y, cv=5, scoring=\"r2\", n_jobs=-1)\n", "print(\"Cross-validation R² scores:\", cv_scores)\n", "print(\"Mean CV R²:\", np.mean(cv_scores))\n", "\n", "plt.figure(figsize=(8, 6))\n", "sns.boxplot(cv_scores)\n", "plt.title(\"Cross-validation R² Distribution\")\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 4, "id": "b145c1d5-35f2-4373-b4c2-d52577d654e0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dataset loaded. X shape: (721, 1499), y shape: (721,)\n", "Starting RandomizedSearchCV...\n", "Fitting 5 folds for each of 40 candidates, totalling 200 fits\n", "\n", "RandomizedSearchCV complete.\n", "Best parameters found:  {'stacker__xgb__n_estimators': 200, 'stacker__xgb__max_depth': 3, 'stacker__xgb__learning_rate': 0.2, 'stacker__rf__n_estimators': 300, 'stacker__rf__max_depth': 5, 'stacker__final_estimator__alpha': 0.1, 'preprocessor__pca__n_components': 100}\n", "Best CV R²:  -1.6124619991649698\n", "\n", "Final Model Evaluation on Test Set:\n", "Mean Squared Error (MSE): 0.0000\n", "R-squared (R²): -1.3559\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Cross-validation R² scores: [-1.16931953 -1.11425555 -1.51761275 -0.91973723 -0.00720421]\n", "Mean CV R²: -0.9456258533859341\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "import numpy as np\n", "from sklearn.model_selection import train_test_split, RandomizedSearchCV, cross_val_score\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.decomposition import PCA\n", "from sklearn.ensemble import RandomForestRegressor, StackingRegressor\n", "from sklearn.linear_model import Ridge\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.metrics import mean_squared_error, r2_score\n", "from sklearn.impute import SimpleImputer\n", "import xgboost as xgb\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# --------------------------\n", "# Load dataset\n", "# --------------------------\n", "df = pd.read_csv('Merged_Cleaned.csv', index_col=0)\n", "\n", "# Features and labels\n", "X = df.iloc[:, 1:]\n", "y = df.iloc[:, 0]\n", "print(f\"Dataset loaded. X shape: {X.shape}, y shape: {y.shape}\")\n", "\n", "# --------------------------\n", "# Train-test split\n", "# --------------------------\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X, y, test_size=0.2, random_state=42\n", ")\n", "\n", "# --------------------------\n", "# Preprocessing pipeline\n", "# --------------------------\n", "preprocessing_pipeline = Pipeline([\n", "    ('imputer', SimpleImputer(strategy='mean')),\n", "    ('scaler', StandardScaler()),\n", "    ('pca', PCA(n_components=150))  # Adjusted components\n", "])\n", "\n", "# --------------------------\n", "# Base models\n", "# --------------------------\n", "base_models = [\n", "    ('rf', RandomForestRegressor(random_state=42, n_jobs=-1)),\n", "    ('xgb', xgb.XGBRegressor(\n", "        random_state=42,\n", "        tree_method='hist',\n", "        n_jobs=-1\n", "    ))\n", "]\n", "\n", "# Meta-learner\n", "meta_learner = Ridge(alpha=1.0)  # Better regularized than LinearRegression\n", "\n", "# Stacking regressor\n", "stacking_regressor = StackingRegressor(\n", "    estimators=base_models,\n", "    final_estimator=meta_learner,\n", "    cv=5,\n", "    n_jobs=-1\n", ")\n", "\n", "# --------------------------\n", "# Full pipeline\n", "# --------------------------\n", "full_pipeline = Pipeline([\n", "    ('preprocessor', preprocessing_pipeline),\n", "    ('stacker', stacking_regressor)\n", "])\n", "\n", "# --------------------------\n", "# Optimized parameter grid\n", "# --------------------------\n", "param_dist = {\n", "    'stacker__rf__n_estimators': [200, 300],\n", "    'stacker__rf__max_depth': [5, 10, None],\n", "    'stacker__xgb__n_estimators': [200, 300],\n", "    'stacker__xgb__max_depth': [3, 5, 7],\n", "    'stacker__xgb__learning_rate': [0.05, 0.1, 0.2],\n", "    'stacker__final_estimator__alpha': [0.1, 1.0, 10.0],\n", "    'preprocessor__pca__n_components': [100, 150, 200]\n", "}\n", "\n", "# --------------------------\n", "# RandomizedSearchCV\n", "# --------------------------\n", "random_search = RandomizedSearchCV(\n", "    full_pipeline,\n", "    param_distributions=param_dist,\n", "    n_iter=40,   # increased iterations for better search\n", "    cv=5,        # more folds\n", "    scoring='r2',  # directly optimize R²\n", "    verbose=2,\n", "    random_state=42,\n", "    n_jobs=-1\n", ")\n", "\n", "print(\"Starting RandomizedSearchCV...\")\n", "random_search.fit(X_train, y_train)\n", "print(\"\\nRandomizedSearchCV complete.\")\n", "\n", "print(\"Best parameters found: \", random_search.best_params_)\n", "print(\"Best CV R²: \", random_search.best_score_)\n", "\n", "# --------------------------\n", "# Best model evaluation\n", "# --------------------------\n", "best_model = random_search.best_estimator_\n", "y_pred = best_model.predict(X_test)\n", "\n", "mse = mean_squared_error(y_test, y_pred)\n", "r2 = r2_score(y_test, y_pred)\n", "\n", "print(f\"\\nFinal Model Evaluation on Test Set:\")\n", "print(f\"Mean Squared Error (MSE): {mse:.4f}\")\n", "print(f\"R-squared (R²): {r2:.4f}\")\n", "\n", "# --------------------------\n", "# Visualization\n", "# --------------------------\n", "sns.set(style=\"whitegrid\")\n", "\n", "# 1. Actual vs Predicted\n", "plt.figure(figsize=(8, 6))\n", "sns.scatterplot(x=y_test, y=y_pred, alpha=0.7)\n", "plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--')\n", "plt.xlabel(\"Actual Values\")\n", "plt.ylabel(\"Predicted Values\")\n", "plt.title(\"Actual vs Predicted\")\n", "plt.show()\n", "\n", "# 2. Residual plot\n", "residuals = y_test - y_pred\n", "plt.figure(figsize=(8, 6))\n", "sns.histplot(residuals, bins=30, kde=True, color=\"blue\")\n", "plt.title(\"Residual Distribution\")\n", "plt.xlabel(\"Residuals\")\n", "plt.show()\n", "\n", "plt.figure(figsize=(8, 6))\n", "sns.scatterplot(x=y_pred, y=residuals, alpha=0.7)\n", "plt.axhline(0, color='red', linestyle='--')\n", "plt.xlabel(\"Predicted Values\")\n", "plt.ylabel(\"Residuals\")\n", "plt.title(\"Residuals vs Predicted\")\n", "plt.show()\n", "\n", "# 3. Cross-validation R²\n", "cv_scores = cross_val_score(best_model, X, y, cv=5, scoring=\"r2\", n_jobs=-1)\n", "print(\"Cross-validation R² scores:\", cv_scores)\n", "print(\"Mean CV R²:\", np.mean(cv_scores))\n", "\n", "plt.figure(figsize=(8, 6))\n", "sns.boxplot(cv_scores)\n", "plt.title(\"Cross-validation R² Distribution\")\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "2e5cf0f0-7b98-4f64-bf85-933876b216d7", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}