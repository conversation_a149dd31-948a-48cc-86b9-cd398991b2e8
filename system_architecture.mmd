graph TB
    %% Hardware Layer
    subgraph Hardware["🔧 Hardware Layer"]
        Package["📦 Medical Package<br/>50-100g"]
        LoadCell["⚖️ Bending Beam Load Cell<br/>• Strain Gauges<br/>• Wheatstone Bridge<br/>• mV Output"]
        HX711["🔌 HX711 ADC<br/>• 24-bit Resolution<br/>• Programmable Gain<br/>• 80Hz Max Rate"]
        MCU["🖥️ Microcontroller<br/>• Raspberry Pi Pico<br/>• STM32<br/>• ARM Cortex-M"]
    end

    %% Signal Processing Layer
    subgraph DSP["🔊 Digital Signal Processing"]
        RawData["📊 Raw Sensor Data<br/>• 50-100 Hz Sampling<br/>• Time Series"]
        
        subgraph Filtering["🔍 Noise Filtering"]
            MovingAvg["📈 Moving Average<br/>• Window: 10, 50, 100<br/>• Smoothing"]
            <PERSON><PERSON>["🎯 Kalman Filter<br/>• Optimal Estimation<br/>• Noise Reduction"]
            Butterworth["🌊 Butterworth Filter<br/>• Flat Response<br/>• Frequency Filtering"]
        end
        
        subgraph FeatureExt["⚙️ Feature Extraction"]
            TimeDomain["⏱️ Time Domain<br/>• Mean, Std, RMS<br/>• Skewness, Kurtosis<br/>• Peak-to-Peak"]
            FreqDomain["📡 Frequency Domain<br/>• FFT Analysis<br/>• Dominant Frequency<br/>• Spectral Energy"]
            Statistical["📊 Statistical<br/>• Correlation Analysis<br/>• F-test Selection<br/>• PCA Reduction"]
        end
    end

    %% Machine Learning Layer
    subgraph ML["🤖 Machine Learning Pipeline"]
        FeatureVector["📋 Feature Vector<br/>• ~200 Features<br/>• Normalized<br/>• Scaled"]
        
        subgraph Level0["🎯 Level-0 Base Models"]
            DT["🌳 Decision Tree<br/>• Max Depth: 8<br/>• Min Samples: 50<br/>• Regularized"]
            RF["🌲 Random Forest<br/>• 200 Estimators<br/>• Bootstrap: True<br/>• OOB Score"]
            GB["📈 Gradient Boosting<br/>• Learning Rate: 0.05<br/>• Shallow Trees<br/>• Subsample: 0.8"]
            XGB["⚡ XGBoost<br/>• L1/L2 Regularization<br/>• Feature Subsampling<br/>• Early Stopping"]
            LGB["🚀 LightGBM<br/>• Fast Training<br/>• Memory Efficient<br/>• High Accuracy"]
        end
        
        subgraph Level1["🎯 Level-1 Meta-Learner"]
            Ridge["📐 Ridge Regression<br/>• Alpha: 50.0<br/>• Strong Regularization<br/>• Combines Predictions"]
        end
        
        FinalPred["🎯 Final Prediction<br/>• Weight Estimate<br/>• Confidence Interval<br/>• Error Bounds"]
    end

    %% Decision Layer
    subgraph Decision["⚖️ Decision Making"]
        Threshold["🎚️ Threshold Check<br/>• Target Weight ± 2g<br/>• Tolerance Band<br/>• Quality Control"]
        Accept["✅ ACCEPT<br/>• Within Tolerance<br/>• Pass to Next Stage<br/>• Log Success"]
        Reject["❌ REJECT<br/>• Outside Tolerance<br/>• Remove from Line<br/>• Log Failure"]
    end

    %% Control System
    subgraph Control["🎮 Control System"]
        Conveyor["🔄 Conveyor Control<br/>• Speed Regulation<br/>• Position Tracking<br/>• Synchronization"]
        Pneumatic["💨 Pneumatic Pusher<br/>• Reject Mechanism<br/>• Fast Response<br/>• Precise Timing"]
        Sorting["📦 Package Sorting<br/>• Accept Route<br/>• Reject Route<br/>• Quality Bins"]
    end

    %% Monitoring System
    subgraph Monitor["📊 Monitoring & Analytics"]
        RealTime["⏱️ Real-Time Dashboard<br/>• Live Metrics<br/>• Performance KPIs<br/>• Alert System"]
        DataLog["📝 Data Logging<br/>• Prediction History<br/>• Error Analysis<br/>• Trend Monitoring"]
        Maintenance["🔧 Maintenance<br/>• Calibration Alerts<br/>• Drift Detection<br/>• Model Updates"]
    end

    %% Load Cell Working Principle
    subgraph LoadCellDetail["⚖️ Load Cell Working Principle"]
        Force["⬇️ Applied Force<br/>F = mg"]
        Deformation["🔄 Beam Deformation<br/>ε = σ/E"]
        StrainGauge["📏 Strain Gauge<br/>ΔR/R = GF × ε"]
        WheatBridge["🔌 Wheatstone Bridge<br/>Vout = Vin × (ΔR/R)"]
        Amplify["📢 Signal Amplification<br/>Gain = 64x - 128x"]
    end

    %% Connections
    Package --> LoadCell
    LoadCell --> HX711
    HX711 --> MCU
    MCU --> RawData
    
    RawData --> Filtering
    MovingAvg --> FeatureExt
    Kalman --> FeatureExt
    Butterworth --> FeatureExt
    
    TimeDomain --> FeatureVector
    FreqDomain --> FeatureVector
    Statistical --> FeatureVector
    
    FeatureVector --> Level0
    DT --> Ridge
    RF --> Ridge
    GB --> Ridge
    XGB --> Ridge
    LGB --> Ridge
    
    Ridge --> FinalPred
    FinalPred --> Threshold
    
    Threshold --> Accept
    Threshold --> Reject
    
    Accept --> Conveyor
    Reject --> Pneumatic
    Conveyor --> Sorting
    Pneumatic --> Sorting
    
    FinalPred --> RealTime
    Accept --> DataLog
    Reject --> DataLog
    DataLog --> Maintenance
    
    %% Load Cell Detail Connections
    Force --> Deformation
    Deformation --> StrainGauge
    StrainGauge --> WheatBridge
    WheatBridge --> Amplify
    Amplify --> HX711

    %% Styling
    classDef hardware fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef dsp fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef ml fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef control fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef monitor fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef detail fill:#e0f2f1,stroke:#004d40,stroke-width:2px

    class Package,LoadCell,HX711,MCU hardware
    class RawData,MovingAvg,Kalman,Butterworth,TimeDomain,FreqDomain,Statistical,FeatureVector dsp
    class DT,RF,GB,XGB,LGB,Ridge,FinalPred ml
    class Threshold,Accept,Reject decision
    class Conveyor,Pneumatic,Sorting control
    class RealTime,DataLog,Maintenance monitor
    class Force,Deformation,StrainGauge,WheatBridge,Amplify detail
